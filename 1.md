# Start everything (already running)
docker-compose up -d

# Development server (already running)
npm run dev
npm run build
npm outdated
npm run lint
npm install


# View logs
docker-compose logs -f

# Stop services
docker-compose down

# Reset database
docker-compose down -v && docker-compose up -d

# db commands
psql -d benefitlens -c "SELECT id, name, domain FROM companies WHERE name ILIKE '%mcdermott%' OR name ILIKE '%mwe%' OR domain = 'mwe.com';"

docker exec -it benefitlens-postgres psql -U benefitlens_user -d benefitlens -c "SELECT id, email, company_id FROM users WHERE email = '<EMAIL>';"

docker exec -it workwell-postgres psql -U workwell_user -d workwell -c "UPDATE users SET company_id = (SELECT id FROM companies WHERE domain = 'mwe.com') WHERE email = '<EMAIL>';"

docker exec benefitlens-postgres pg_dump -U benefitlens_user -d benefitlens | gzip > backup_$(date +%Y%m%d_%H%M%S).sql.gz

## migrate command
docker exec -i benefitlens-postgres psql -U benefitlens_user -d benefitlens -c "\d benefit_categories"
docker exec -i benefitlens-postgres psql -U benefitlens_user -d benefitlens -f /dev/stdin < database/migrations/020-remove-is-active-from-benefit-categories.sql


# curl commands
curl -X GET http://localhost:3000/api/admin/activities \
  -H "Cookie: $(curl -s -c - -X POST http://localhost:3000/api/auth/magic-link -H 'Content-Type: application/json' -d '{\"token\": \"d6b4e6183ae2f0c9740c07cc2ea9ae6991ba1d7c0625cf7cec7967eff2099ba9\"}' | grep -o 'session_token=[^;]*')"

# Quick demo imports
npm run import:demo-companies:dry-run
npm run import:demo-benefits:dry-run

# Custom file imports
node scripts/import-data.js --type=companies --file=my-companies.csv
node scripts/import-data.js --type=companies --file=data/german-stock-indices-companies.json --overwrite

node scripts/import-data.js --type=benefits --file=my-benefits.json --category=health
node scripts/import-company-benefits.js --file=data/german-stock-indices-company-benefits.json --unverified --dry-run
node scripts/import-company-benefits.js --file=data/german-stock-indices-company-benefits.json --unverified
node scripts/import-company-benefits.js --file=data/german-stock-indices-company-benefits.json --unverified --overwrite

# Test App
npx tsc --noEmit 2>&1 | wc -l
npx tsc --noEmit 2>&1 | head -50
npm test
npm lint
npm run test:e2e:full -- --reporter=list
npm run test:e2e -- essential-tests.spec.ts
DISABLE_RATE_LIMITING=true npm run test:e2e -- user-journeys.spec.ts --grep "Error Handling Journey"
npx playwright test --config=playwright-no-rate-limit.config.ts --project="iPhone 14" src/__tests__/e2e/mobile-functionality-tests.spec.ts
npm test -- --run src/__tests__/email-template-readability.test.ts
npm test -- --run src/__tests__/user-authentication.test.ts
npm test -- --run src/__tests__/components/magic-link-verification.test.tsx
npm run test:e2e -- src/__tests__/e2e/user-journeys.spec.ts --grep "User Benefit Management Journey" --workers=3 --reporter=line
npm run test:e2e -- src/__tests__/e2e/user-journeys.spec.ts --grep "User Benefit Ranking Journey" --workers=3 --reporter=line
npm run test:e2e -- src/__tests__/e2e/user-journeys.spec.ts --grep "User Benefit Ranking Journey" --project="iPad Pro"
npm run test:e2e -- src/__tests__/e2e/user-journeys.spec.ts --grep "User Benefit Ranking Journey" --project="chromium"
npm run test:e2e -- src/__tests__/e2e/user-journeys.spec.ts --grep "User Benefit Ranking Journey" --project="Mobile Chrome"
npm run test:e2e -- srvc/__tests__/e2e/benefit-dispute-journey.spec.ts

# With custom field mappings
node scripts/import-data.js --type=companies --file=data.csv --mapping=custom.json


# Health checks - ALL PASSING ✅
npm run health:check     # ✅ Comprehensive health check
npm run health:simple    # ✅ Simple health check

# Environment validation - PASSING ✅  
npm run validate:env     # ✅ All required variables validated

# Application - RUNNING ✅
npm run dev             # ✅ Server running on http://localhost:3000
npm run build           # ✅ Build successful



# SETUP ACTION RUNNER
https://docs.github.com/en/actions/how-tos/manage-runners/self-hosted-runners/add-runners
https://docs.github.com/en/actions/how-tos/manage-runners/self-hosted-runners/configure-the-application
https://docs.github.com/en/actions/how-tos/deploy/configure-and-manage-deployments/control-deployments

# DockerHub Repo
https://hub.docker.com/repository/docker/rgarcia89/benefitlens/general

# Raspberry Pi 
https://wolfpaulus.com/rp5-cli/

## GitHub SSH Key
ssh-keygen -t ed25519 -C "$(whoami)@$(hostname) GitHub" -f ~/.ssh/id_ed25519
# press Enter for default location, optionally set a passphrase
eval "$(ssh-agent -s)"
ssh-add ~/.ssh/id_ed25519
cat ~/.ssh/id_ed25519.pub
mkdir -p ~/.ssh
chmod 700 ~/.ssh
cat >> ~/.ssh/config <<'EOF'
Host github.com
  HostName github.com
  User git
  IdentityFile ~/.ssh/id_ed25519
  IdentitiesOnly yes
  AddKeysToAgent yes
EOF
chmod 700 ~/.ssh
chmod 600 ~/.ssh/id_ed25519 ~/.ssh/id_rsa 2>/dev/null || true
chmod 644 ~/.ssh/id_ed25519.pub ~/.ssh/id_rsa.pub 2>/dev/null || true
