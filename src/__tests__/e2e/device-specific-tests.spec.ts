/**
 * Device-Specific E2E Tests
 * Tests functionality across different devices including iPhone and MacBook
 */

import { test, expect } from '@playwright/test'
import { waitForPageLoad } from './auth-helpers'

test.describe('Device-Specific Tests', () => {
  test('iPhone Device Functionality', async ({ page, browserName }) => {
    // Skip if not running on iPhone-like device
    const viewport = page.viewportSize()
    const isIPhone = viewport && viewport.width <= 428 && viewport.height >= 800
    
    if (!isIPhone) {
      test.skip()
    }

    // 1. Visit homepage
    await page.goto('/')
    await waitForPageLoad(page)
    
    // 2. Verify mobile-optimized layout
    expect(await page.title()).toContain('BenefitLens')
    
    // 3. Test touch interactions - search functionality
    const searchInput = page.locator('input[placeholder*="Search for benefits or companies"]')
    await expect(searchInput).toBeVisible()
    
    // Test touch input
    await searchInput.tap()
    await searchInput.fill('Tech')
    await searchInput.press('Enter')
    await waitForPageLoad(page)
    
    // 4. Verify search results are displayed properly on iPhone
    const results = page.locator('.company-card, [data-testid="company-card"], .search-result')
    await expect(results.first()).toBeVisible({ timeout: 10000 })
    
    // 5. Test mobile navigation
    const navElements = [
      page.locator('text=Companies'),
      page.locator('text=Benefits'),
      page.locator('a[href="/companies"]'),
      page.locator('a[href="/benefits"]'),
    ]
    
    let navigationFound = false
    for (const element of navElements) {
      if (await element.isVisible()) {
        navigationFound = true
        break
      }
    }
    
    expect(navigationFound).toBe(true)
    
    console.log('✅ iPhone functionality verified')
  })

  test('MacBook Device Functionality', async ({ page }) => {
    // Skip if not running on MacBook-like device
    const viewport = page.viewportSize()
    const isMacBook = viewport && viewport.width >= 1366 && viewport.height >= 768
    
    if (!isMacBook) {
      test.skip()
    }

    // 1. Visit homepage
    await page.goto('/')
    await waitForPageLoad(page)
    
    // 2. Verify desktop layout
    expect(await page.title()).toContain('BenefitLens')
    
    // 3. Test desktop-specific features
    const searchInput = page.locator('input[placeholder*="Search for benefits or companies"]')
    await expect(searchInput).toBeVisible()
    
    // Test keyboard shortcuts and desktop interactions
    await searchInput.click()
    await searchInput.fill('E2E Tech')
    await searchInput.press('Enter')
    await waitForPageLoad(page)
    
    // 4. Verify search results display properly on larger screen
    const results = page.locator('.company-card, [data-testid="company-card"], .search-result')
    await expect(results.first()).toBeVisible({ timeout: 10000 })
    
    // 5. Test desktop navigation
    await expect(page.locator('text=Companies')).toBeVisible()
    await expect(page.locator('text=Benefits')).toBeVisible()
    
    // 6. Test hover interactions (desktop-specific)
    const firstResult = results.first()
    await firstResult.hover()
    
    // 7. Test multi-column layout if available
    const companyCards = page.locator('.company-card, [data-testid="company-card"]')
    const cardCount = await companyCards.count()
    
    if (cardCount > 1) {
      // Verify cards are displayed in a grid layout on desktop
      const firstCard = companyCards.first()
      const secondCard = companyCards.nth(1)
      
      const firstCardBox = await firstCard.boundingBox()
      const secondCardBox = await secondCard.boundingBox()
      
      if (firstCardBox && secondCardBox) {
        // On desktop, cards should be side by side or in a grid
        const isGridLayout = Math.abs(firstCardBox.y - secondCardBox.y) < 50 // Same row
        console.log(isGridLayout ? '✅ Desktop grid layout detected' : '✅ Desktop list layout detected')
      }
    }
    
    console.log('✅ MacBook functionality verified')
  })

  test('iPad Device Functionality', async ({ page }) => {
    // Skip if not running on iPad-like device
    const viewport = page.viewportSize()
    const isIPad = viewport && viewport.width >= 768 && viewport.width <= 1024
    
    if (!isIPad) {
      test.skip()
    }

    // 1. Visit homepage
    await page.goto('/')
    await waitForPageLoad(page)
    
    // 2. Verify tablet layout
    expect(await page.title()).toContain('BenefitLens')
    
    // 3. Test tablet-specific interactions
    const searchInput = page.locator('input[placeholder*="Search for benefits or companies"]')
    await expect(searchInput).toBeVisible()
    
    // Test touch input on tablet
    await searchInput.tap()
    await searchInput.fill('Tech Corp')
    await searchInput.press('Enter')
    await waitForPageLoad(page)
    
    // 4. Verify search results
    const results = page.locator('.company-card, [data-testid="company-card"], .search-result')
    await expect(results.first()).toBeVisible({ timeout: 10000 })
    
    // 5. Test tablet navigation
    await expect(page.locator('text=Companies')).toBeVisible()
    await expect(page.locator('text=Benefits')).toBeVisible()
    
    console.log('✅ iPad functionality verified')
  })

  test('Cross-Device Responsive Design', async ({ page }) => {
    // Test that the app responds correctly to viewport changes
    
    // 1. Start with mobile viewport
    await page.setViewportSize({ width: 375, height: 667 }) // iPhone SE
    await page.goto('/')
    await waitForPageLoad(page)
    
    // Verify mobile layout
    const searchInputMobile = page.locator('input[placeholder*="Search for benefits or companies"]')
    await expect(searchInputMobile).toBeVisible()
    
    // 2. Switch to tablet viewport
    await page.setViewportSize({ width: 768, height: 1024 }) // iPad
    await page.waitForTimeout(1000) // Allow layout to adjust
    
    // Verify tablet layout
    await expect(searchInputMobile).toBeVisible()
    
    // 3. Switch to desktop viewport
    await page.setViewportSize({ width: 1440, height: 900 }) // MacBook
    await page.waitForTimeout(1000) // Allow layout to adjust
    
    // Verify desktop layout - use first() to avoid strict mode violations
    await expect(searchInputMobile).toBeVisible()
    await expect(page.locator('text=Companies').first()).toBeVisible()
    await expect(page.locator('text=Benefits').first()).toBeVisible()
    
    console.log('✅ Responsive design verified across viewports')
  })

  test('Touch vs Mouse Interactions', async ({ page, isMobile }) => {
    await page.goto('/')
    await waitForPageLoad(page)
    
    const searchInput = page.locator('input[placeholder*="Search for benefits or companies"]')
    await expect(searchInput).toBeVisible()
    
    if (isMobile) {
      // Test touch interactions
      await searchInput.tap()
      await searchInput.fill('E2E')  // Use a search term that will return results
      console.log('✅ Touch interactions working')
    } else {
      // Test mouse interactions
      await searchInput.click()
      await searchInput.fill('E2E')  // Use a search term that will return results
      
      // Test hover effects (desktop only)
      const navLinks = page.locator('a[href="/companies"], a[href="/benefits"]')
      if (await navLinks.first().isVisible()) {
        await navLinks.first().hover()
        console.log('✅ Mouse hover interactions working')
      }
    }
    
    await searchInput.press('Enter')
    await waitForPageLoad(page)
    
    // Verify search works regardless of input method - check for any visible content
    const companyCards = page.locator('.company-card, [data-testid="company-card"], .search-result').first()
    const foundText = page.locator('text=Found')

    // Check if either company cards or "Found X companies" text is visible
    const hasCompanyCards = await companyCards.isVisible({ timeout: 5000 }).catch(() => false)
    const hasFoundText = await foundText.isVisible({ timeout: 5000 }).catch(() => false)

    expect(hasCompanyCards || hasFoundText).toBe(true)
    
    console.log('✅ Input method compatibility verified')
  })
})
