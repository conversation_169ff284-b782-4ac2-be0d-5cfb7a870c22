/**
 * E2E Test Authentication Helpers
 * Provides proper authentication flow for e2e tests
 */

import { Page } from '@playwright/test'
import { Pool } from 'pg'

// Database connection for creating unique tokens
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
})

/**
 * Create a unique magic link token for testing
 */
async function createUniqueToken(email: string): Promise<string> {
  // Create a highly unique token with timestamp, random components, worker ID, and process ID
  const timestamp = Date.now()
  const random = Math.random().toString(36).substring(2)
  const workerId = process.env.PLAYWRIGHT_WORKER_INDEX || '0'
  const processId = process.pid
  const emailHash = email.replace(/[^a-zA-Z0-9]/g, '')
  const uniqueToken = `test-token-${timestamp}-${random}-${workerId}-${processId}-${emailHash}`
  const futureExpiry = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours from now

  // Use a transaction to ensure atomic cleanup and creation
  const client = await pool.connect()
  try {
    await client.query('BEGIN')

    // First, clean up any existing tokens for this email to prevent conflicts
    await client.query(`
      DELETE FROM magic_link_tokens
      WHERE email = $1
    `, [email])

    // Insert the new token
    await client.query(`
      INSERT INTO magic_link_tokens (token, email, expires_at, used_at)
      VALUES ($1, $2, $3, null)
    `, [uniqueToken, email, futureExpiry])

    await client.query('COMMIT')

    // Add a small delay to ensure the transaction is fully committed
    await new Promise(resolve => setTimeout(resolve, 200))

    return uniqueToken
  } catch (error) {
    await client.query('ROLLBACK')
    console.error('Error creating unique token:', error)
    throw error
  } finally {
    client.release()
  }
}

/**
 * Simple and reliable sign in function using magic links
 */
export async function signInUser(page: Page, email: string = 'user1@techcorp.e2e') {
  console.log(`🔐 Signing in user: ${email}`)

  // Create a fresh token for this authentication attempt
  const token = await createUniqueToken(email)
  console.log(`🎫 Created token: ${token}`)

  // Go directly to the magic link URL (simulating clicking the email link)
  await page.goto(`/auth/magic-link#${token}`)

  try {
    // Wait for page to load and process the token
    await page.waitForTimeout(1000)

    // Wait for either the confirmation button or an error message with extended timeout for parallel load
    await Promise.race([
      page.waitForSelector('button:has-text("Complete Sign In")', { timeout: 30000 }),
      page.waitForSelector('.bg-red-50', { timeout: 30000 }).catch(() => null),
      page.waitForSelector('text=Invalid', { timeout: 30000 }).catch(() => null)
    ])

    // Check if we have the confirmation button
    const confirmButton = page.locator('button:has-text("Complete Sign In")').first()
    if (await confirmButton.isVisible()) {
      await confirmButton.click()
      await page.waitForURL(/\/(dashboard|$)/, { timeout: 15000 })
      console.log('✅ User authentication successful')
    } else {
      throw new Error('Confirmation button not found')
    }
  } catch (error) {
    console.log('Magic link verification failed, creating new token and retrying...')

    // Create a new token and retry once
    const newToken = await createUniqueToken(email)
    console.log(`🔄 Retrying with new token: ${newToken}`)
    await page.goto(`/auth/magic-link#${newToken}`)

    // Wait for page to load and process the token
    await page.waitForTimeout(1500)

    // Wait for confirmation button and click it with extended timeout for parallel load
    await Promise.race([
      page.waitForSelector('button:has-text("Complete Sign In")', { timeout: 30000 }),
      page.waitForSelector('.bg-red-50', { timeout: 30000 }).catch(() => null),
      page.waitForSelector('text=Invalid', { timeout: 30000 }).catch(() => null)
    ])

    const retryConfirmButton = page.locator('button:has-text("Complete Sign In")').first()
    if (await retryConfirmButton.isVisible()) {
      await retryConfirmButton.click()
      await page.waitForURL(/\/(dashboard|$)/, { timeout: 15000 })
      console.log('✅ User authentication successful on retry')
    } else {
      throw new Error('Authentication failed after retry - confirmation button not available')
    }
  }
}

/**
 * Simple and reliable admin sign in function using magic links
 */
export async function signInAdmin(page: Page, email: string = '<EMAIL>') {
  console.log(`🔐 Signing in admin: ${email}`)

  // Create a fresh token for this authentication attempt
  const token = await createUniqueToken(email)
  console.log(`🎫 Created admin token: ${token}`)

  // Go directly to the magic link URL with fragment (simulating clicking the email link)
  await page.goto(`/auth/magic-link#${token}`)

  // Give the page a moment to fully load and start validation
  await page.waitForTimeout(1000)

  try {
    // Wait for either the confirmation button or an error message with extended timeout for parallel load
    await Promise.race([
      page.waitForSelector('button:has-text("Complete Sign In")', { timeout: 25000 }),
      page.waitForSelector('.bg-red-50', { timeout: 25000 }).catch(() => null),
      page.waitForSelector('text=Invalid', { timeout: 25000 }).catch(() => null)
    ])

    // Check if we have the confirmation button
    const confirmButton = await page.locator('button:has-text("Complete Sign In")').first()
    if (await confirmButton.isVisible()) {
      await confirmButton.click()
      await page.waitForURL(/\/(admin|dashboard)/, { timeout: 15000 })
      console.log('✅ Admin authentication successful')
    } else {
      throw new Error('Admin confirmation button not found')
    }
  } catch (error) {
    console.log('Admin magic link verification failed, creating new token and retrying...')

    // Create a new token and retry once
    const newToken = await createUniqueToken(email)
    console.log(`🔄 Retrying admin auth with new token: ${newToken}`)
    await page.goto(`/auth/magic-link#${newToken}`)

    // Wait for confirmation button and click it with extended timeout for parallel load
    await Promise.race([
      page.waitForSelector('button:has-text("Complete Sign In")', { timeout: 25000 }),
      page.waitForSelector('.bg-red-50', { timeout: 25000 }).catch(() => null),
      page.waitForSelector('text=Invalid', { timeout: 25000 }).catch(() => null)
    ])

    const retryConfirmButton = await page.locator('button:has-text("Complete Sign In")').first()
    if (await retryConfirmButton.isVisible()) {
      await retryConfirmButton.click()
      await page.waitForURL(/\/(admin|dashboard)/, { timeout: 15000 })
      console.log('✅ Admin authentication successful on retry')
    } else {
      throw new Error('Admin authentication failed after retry - confirmation button not available')
    }
  }
}

/**
 * Sign in a super admin user using the proper API flow
 */
export async function signInSuperAdmin(page: Page, email: string = '<EMAIL>', token?: string) {
  // Create a unique token if none provided
  if (!token) {
    token = await createUniqueToken(email)
  }

  // Navigate to sign-in page
  await page.goto('/sign-in')

  // Fill in email and submit
  await page.fill('input[type="email"]', email)
  await page.click('button[type="submit"]')

  // Wait for the magic link sent message - use partial text match and look for success styling
  await Promise.race([
    page.waitForSelector('text=Magic link sent!', { timeout: 15000 }),
    page.waitForSelector('.bg-green-50:has-text("Magic link sent")', { timeout: 15000 }),
    page.waitForSelector('[class*="green"]:has-text("Magic link")', { timeout: 15000 })
  ])

  // Now simulate clicking the magic link by navigating to the magic link page
  await page.goto(`/auth/magic-link#${token}`)

  // Wait for the confirmation button and click it
  await page.waitForSelector('button:has-text("Complete Sign In")', { timeout: 10000 })
  await page.click('button:has-text("Complete Sign In")')

  // Wait for verification to complete and redirect to dashboard
  await page.waitForURL(/\/(admin|dashboard)/, { timeout: 15000 })
}

/**
 * Authenticate using a magic link token via the API
 */
async function authenticateWithToken(page: Page, token: string) {
  // Call the magic link verification API directly
  const response = await page.evaluate(async (authToken) => {
    const res = await fetch('/api/auth/magic-link', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ token: authToken }),
    })
    
    return {
      ok: res.ok,
      status: res.status,
      data: await res.json()
    }
  }, token)
  
  if (!response.ok) {
    throw new Error(`Authentication failed: ${response.status} - ${JSON.stringify(response.data)}`)
  }
  
  // Wait a moment for the session to be established
  await page.waitForTimeout(1000)
}

/**
 * Sign up a new user using the proper API flow
 */
export async function signUpUser(page: Page, email: string, firstName: string, lastName: string) {
  // Navigate to sign-up page
  await page.goto('/sign-up')

  // Fill in the form
  await page.fill('input[name="email"]', email)
  await page.fill('input[name="firstName"]', firstName)
  await page.fill('input[name="lastName"]', lastName)
  await page.click('button[type="submit"]')

  // Wait for the magic link sent message
  await page.waitForSelector('text=Magic link sent', { timeout: 10000 })

  // For testing, we'll create a sign-up token and authenticate directly
  // In a real scenario, this would be handled by clicking the email link
  const signUpToken = `mock-signup-${email.replace('@', '-').replace('.', '-')}`

  // Create the sign-up token in the database via direct API call
  await page.evaluate(async (tokenData) => {
    // Create a magic link token for sign-up with user data
    const response = await fetch('/api/test/create-signup-token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(tokenData),
    })

    if (!response.ok) {
      console.warn('Failed to create sign-up token, using fallback')
    }
  }, { token: signUpToken, email, firstName, lastName })

  // Authenticate with the sign-up token
  await authenticateWithToken(page, signUpToken)

  // Wait for redirect to dashboard
  await page.waitForURL(/\/dashboard/, { timeout: 10000 })
}

/**
 * Wait for page to fully load with optimized strategy
 */
export async function waitForPageLoad(page: Page) {
  // Wait for network to be mostly idle instead of fixed timeout
  await page.waitForLoadState('domcontentloaded')
  await page.waitForLoadState('networkidle', { timeout: 10000 }).catch(() => {
    // If networkidle times out, fall back to a shorter wait
    return page.waitForTimeout(1000)
  })
}

/**
 * Clear all authentication state
 */
export async function clearAuth(page: Page) {
  await page.context().clearCookies()

  // Navigate to a page first to ensure we have access to localStorage
  try {
    await page.goto('/')
    await page.evaluate(() => {
      localStorage.clear()
      sessionStorage.clear()
    })
  } catch (error) {
    // If localStorage access fails, just clear cookies (which is the main auth mechanism)
    console.warn('Could not clear localStorage/sessionStorage:', error)
  }
}
